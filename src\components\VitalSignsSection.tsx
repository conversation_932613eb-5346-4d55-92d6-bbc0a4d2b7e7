import { useState, useEffect } from "react";
import { Plus, Heart, Thermometer, Activity, Droplets } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";

interface VitalSigns {
  id: string;
  date: string;
  heart_rate: number;
  blood_pressure_systolic: number;
  blood_pressure_diastolic: number;
  temperature: number;
  respiratory_rate: number;
  oxygen_saturation: number;
  on_ventilator: boolean;
  on_support: boolean;
}

interface VitalSignsSectionProps {
  patientId: string;
}

const VitalSignsSection = ({ patientId }: VitalSignsSectionProps) => {
  const [vitals, setVitals] = useState<VitalSigns[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    heart_rate: "",
    blood_pressure_systolic: "",
    blood_pressure_diastolic: "",
    temperature: "",
    respiratory_rate: "",
    oxygen_saturation: "",
    on_ventilator: false,
    on_support: false,
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchVitals();
  }, [patientId]);

  const fetchVitals = async () => {
    try {
      const { data, error } = await supabase
        .from("vital_signs")
        .select("*")
        .eq("patient_id", patientId)
        .order("date", { ascending: false });

      if (error) throw error;
      setVitals(data || []);
    } catch (error) {
      console.error("Error fetching vitals:", error);
      toast({
        title: "Error",
        description: "Failed to fetch vital signs",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const { error } = await supabase.from("vital_signs").upsert(
        {
          patient_id: patientId,
          date: formData.date,
          heart_rate: parseInt(formData.heart_rate) || null,
          blood_pressure_systolic: parseInt(formData.blood_pressure_systolic) || null,
          blood_pressure_diastolic: parseInt(formData.blood_pressure_diastolic) || null,
          temperature: parseFloat(formData.temperature) || null,
          respiratory_rate: parseInt(formData.respiratory_rate) || null,
          oxygen_saturation: parseInt(formData.oxygen_saturation) || null,
          on_ventilator: formData.on_ventilator,
          on_support: formData.on_support,
        },
        { onConflict: "patient_id,date" }
      );

      if (error) throw error;

      toast({
        title: "Success",
        description: "Vital signs saved successfully",
      });

      setIsDialogOpen(false);
      fetchVitals();
      resetForm();
    } catch (error: any) {
      console.error("Error saving vitals:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to save vital signs",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      date: new Date().toISOString().split('T')[0],
      heart_rate: "",
      blood_pressure_systolic: "",
      blood_pressure_diastolic: "",
      temperature: "",
      respiratory_rate: "",
      oxygen_saturation: "",
      on_ventilator: false,
      on_support: false,
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Heart className="h-5 w-5 text-red-500" />
          Vital Signs
        </CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Vitals
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add Vital Signs</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date">Date</Label>
                  <Input
                    id="date"
                    type="date"
                    value={formData.date}
                    onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="heart_rate">Heart Rate (bpm)</Label>
                  <Input
                    id="heart_rate"
                    type="number"
                    value={formData.heart_rate}
                    onChange={(e) => setFormData({ ...formData, heart_rate: e.target.value })}
                    placeholder="60-100"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bp_systolic">Systolic BP (mmHg)</Label>
                  <Input
                    id="bp_systolic"
                    type="number"
                    value={formData.blood_pressure_systolic}
                    onChange={(e) => setFormData({ ...formData, blood_pressure_systolic: e.target.value })}
                    placeholder="120"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bp_diastolic">Diastolic BP (mmHg)</Label>
                  <Input
                    id="bp_diastolic"
                    type="number"
                    value={formData.blood_pressure_diastolic}
                    onChange={(e) => setFormData({ ...formData, blood_pressure_diastolic: e.target.value })}
                    placeholder="80"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="temperature">Temperature (°C)</Label>
                  <Input
                    id="temperature"
                    type="number"
                    step="0.1"
                    value={formData.temperature}
                    onChange={(e) => setFormData({ ...formData, temperature: e.target.value })}
                    placeholder="36.5"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="respiratory_rate">Respiratory Rate (/min)</Label>
                  <Input
                    id="respiratory_rate"
                    type="number"
                    value={formData.respiratory_rate}
                    onChange={(e) => setFormData({ ...formData, respiratory_rate: e.target.value })}
                    placeholder="12-20"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="oxygen_saturation">Oxygen Saturation (%)</Label>
                  <Input
                    id="oxygen_saturation"
                    type="number"
                    value={formData.oxygen_saturation}
                    onChange={(e) => setFormData({ ...formData, oxygen_saturation: e.target.value })}
                    placeholder="95-100"
                  />
                </div>
              </div>
              
              <div className="flex flex-col space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="ventilator"
                    checked={formData.on_ventilator}
                    onCheckedChange={(checked) => setFormData({ ...formData, on_ventilator: checked })}
                  />
                  <Label htmlFor="ventilator">On Ventilator</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="support"
                    checked={formData.on_support}
                    onCheckedChange={(checked) => setFormData({ ...formData, on_support: checked })}
                  />
                  <Label htmlFor="support">On Life Support</Label>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Save Vitals</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-8">Loading vital signs...</div>
        ) : vitals.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No vital signs recorded yet. Click "Add Vitals" to get started.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>HR (bpm)</TableHead>
                  <TableHead>BP (mmHg)</TableHead>
                  <TableHead>Temp (°C)</TableHead>
                  <TableHead>RR (/min)</TableHead>
                  <TableHead>O2 Sat (%)</TableHead>
                  <TableHead>Ventilator</TableHead>
                  <TableHead>Support</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {vitals.map((vital) => (
                  <TableRow key={vital.id}>
                    <TableCell>{formatDate(vital.date)}</TableCell>
                    <TableCell>{vital.heart_rate || "-"}</TableCell>
                    <TableCell>
                      {vital.blood_pressure_systolic && vital.blood_pressure_diastolic
                        ? `${vital.blood_pressure_systolic}/${vital.blood_pressure_diastolic}`
                        : "-"}
                    </TableCell>
                    <TableCell>{vital.temperature || "-"}</TableCell>
                    <TableCell>{vital.respiratory_rate || "-"}</TableCell>
                    <TableCell>{vital.oxygen_saturation || "-"}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        vital.on_ventilator 
                          ? "bg-red-100 text-red-800" 
                          : "bg-green-100 text-green-800"
                      }`}>
                        {vital.on_ventilator ? "Yes" : "No"}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        vital.on_support 
                          ? "bg-red-100 text-red-800" 
                          : "bg-green-100 text-green-800"
                      }`}>
                        {vital.on_support ? "Yes" : "No"}
                      </span>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VitalSignsSection;