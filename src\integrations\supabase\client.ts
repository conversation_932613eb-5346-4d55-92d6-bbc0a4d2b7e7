// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://mjvzxraluekwyzeppbcn.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1qdnp4cmFsdWVrd3l6ZXBwYmNuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0Mzg2MTEsImV4cCI6MjA3MDAxNDYxMX0.qVfqH5AE3YI005jp_A2A0TFPhUbTugUHlj2HM0uDmoQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});