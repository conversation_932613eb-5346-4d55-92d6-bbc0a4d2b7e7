import { useState, useEffect } from "react";
import { Plus, FileText, Calendar, User } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";

interface DoctorNote {
  id: string;
  note_date: string;
  note_content: string;
  doctor_name: string;
  created_at: string;
}

interface DoctorNotesSectionProps {
  patientId: string;
}

const DoctorNotesSection = ({ patientId }: DoctorNotesSectionProps) => {
  const [notes, setNotes] = useState<DoctorNote[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    note_date: new Date().toISOString().split('T')[0],
    note_content: "",
    doctor_name: "",
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchNotes();
  }, [patientId]);

  const fetchNotes = async () => {
    try {
      const { data, error } = await supabase
        .from("doctor_notes")
        .select("*")
        .eq("patient_id", patientId)
        .order("note_date", { ascending: false });

      if (error) throw error;
      setNotes(data || []);
    } catch (error) {
      console.error("Error fetching notes:", error);
      toast({
        title: "Error",
        description: "Failed to fetch doctor notes",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const { error } = await supabase.from("doctor_notes").insert({
        patient_id: patientId,
        note_date: formData.note_date,
        note_content: formData.note_content,
        doctor_name: formData.doctor_name,
      });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Note added successfully",
      });

      setIsDialogOpen(false);
      fetchNotes();
      resetForm();
    } catch (error: any) {
      console.error("Error adding note:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to add note",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      note_date: new Date().toISOString().split('T')[0],
      note_content: "",
      doctor_name: "",
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-purple-500" />
          Doctor Notes
        </CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Note
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add Doctor Note</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="note_date">Date</Label>
                  <Input
                    id="note_date"
                    type="date"
                    value={formData.note_date}
                    onChange={(e) => setFormData({ ...formData, note_date: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="doctor_name">Doctor Name</Label>
                  <Input
                    id="doctor_name"
                    value={formData.doctor_name}
                    onChange={(e) => setFormData({ ...formData, doctor_name: e.target.value })}
                    placeholder="Enter doctor's name"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="note_content">Note Content</Label>
                <Textarea
                  id="note_content"
                  value={formData.note_content}
                  onChange={(e) => setFormData({ ...formData, note_content: e.target.value })}
                  placeholder="Enter your clinical notes here..."
                  className="min-h-[150px]"
                  required
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Add Note</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-8">Loading doctor notes...</div>
        ) : notes.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No notes recorded yet. Click "Add Note" to get started.
          </div>
        ) : (
          <div className="space-y-4">
            {notes.map((note) => (
              <Card key={note.id} className="border-l-4 border-l-primary">
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      {formatDate(note.note_date)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Added: {formatDate(note.created_at)} at {formatTime(note.created_at)}
                    </div>
                  </div>
                  
                  {note.doctor_name && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                      <User className="h-4 w-4" />
                      Dr. {note.doctor_name}
                    </div>
                  )}
                  
                  <div className="prose prose-sm max-w-none">
                    <p className="whitespace-pre-wrap text-sm leading-relaxed">
                      {note.note_content}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DoctorNotesSection;