import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { ArrowLeft, Plus, Heart, Thermometer, Activity, Droplets, User, Calendar, FileText } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import VitalSignsSection from "@/components/VitalSignsSection";
import LabValuesSection from "@/components/LabValuesSection";
import MedicationsSection from "@/components/MedicationsSection";
import DoctorNotesSection from "@/components/DoctorNotesSection";

interface Patient {
  id: string;
  patient_id: string;
  name: string;
  gender: string;
  age: number;
  weight: number;
  admission_date: string;
  main_complaint: string;
  history: string;
  initial_diagnosis: string;
}

const PatientDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [patient, setPatient] = useState<Patient | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchPatient();
    }
  }, [id]);

  const fetchPatient = async () => {
    try {
      const { data, error } = await supabase
        .from("patients")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      setPatient(data);
    } catch (error) {
      console.error("Error fetching patient:", error);
      toast({
        title: "Error",
        description: "Failed to fetch patient details",
        variant: "destructive",
      });
      navigate("/");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getGenderBadgeColor = (gender: string) => {
    switch (gender.toLowerCase()) {
      case "male":
        return "bg-blue-100 text-blue-800";
      case "female":
        return "bg-pink-100 text-pink-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Activity className="h-8 w-8 animate-pulse mx-auto mb-4 text-primary" />
          <p>Loading patient details...</p>
        </div>
      </div>
    );
  }

  if (!patient) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <p>Patient not found</p>
          <Button onClick={() => navigate("/")} className="mt-4">
            Return to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate("/")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Dashboard
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
              <User className="h-8 w-8 text-primary" />
              {patient.name}
            </h1>
            <p className="text-muted-foreground mt-1">
              Patient ID: {patient.patient_id}
            </p>
          </div>
        </div>

        {/* Patient Info Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Patient Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Gender</p>
                <Badge className={getGenderBadgeColor(patient.gender)}>
                  {patient.gender}
                </Badge>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Age</p>
                <p className="text-lg font-semibold">{patient.age} years</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Weight</p>
                <p className="text-lg font-semibold">
                  {patient.weight ? `${patient.weight} kg` : "Not recorded"}
                </p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Admission Date
                </p>
                <p className="text-lg font-semibold">{formatDate(patient.admission_date)}</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Main Complaint</p>
                <p className="text-sm">{patient.main_complaint}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Medical History</p>
                <p className="text-sm">{patient.history || "No history recorded"}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Initial Diagnosis</p>
                <p className="text-sm">{patient.initial_diagnosis}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs for different sections */}
        <Tabs defaultValue="vitals" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="vitals" className="flex items-center gap-2">
              <Heart className="h-4 w-4" />
              Vital Signs
            </TabsTrigger>
            <TabsTrigger value="labs" className="flex items-center gap-2">
              <Droplets className="h-4 w-4" />
              Lab Values
            </TabsTrigger>
            <TabsTrigger value="medications" className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Medications
            </TabsTrigger>
            <TabsTrigger value="notes" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Doctor Notes
            </TabsTrigger>
          </TabsList>

          <TabsContent value="vitals">
            <VitalSignsSection patientId={patient.id} />
          </TabsContent>

          <TabsContent value="labs">
            <LabValuesSection patientId={patient.id} />
          </TabsContent>

          <TabsContent value="medications">
            <MedicationsSection patientId={patient.id} />
          </TabsContent>

          <TabsContent value="notes">
            <DoctorNotesSection patientId={patient.id} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default PatientDetail;