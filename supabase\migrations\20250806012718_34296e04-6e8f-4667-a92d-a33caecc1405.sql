-- Create patients table
CREATE TABLE public.patients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    gender VARCHAR(10) NOT NULL CHECK (gender IN ('Male', 'Female', 'Other')),
    age INTEGER NOT NULL CHECK (age > 0 AND age <= 150),
    weight DECIMAL(5,2) CHECK (weight > 0),
    admission_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    main_complaint TEXT,
    history TEXT,
    initial_diagnosis TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create vital_signs table
CREATE TABLE public.vital_signs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID REFERENCES public.patients(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    heart_rate INTEGER CHECK (heart_rate > 0 AND heart_rate <= 300),
    blood_pressure_systolic INTEGER CHECK (blood_pressure_systolic > 0 AND blood_pressure_systolic <= 300),
    blood_pressure_diastolic INTEGER CHECK (blood_pressure_diastolic > 0 AND blood_pressure_diastolic <= 200),
    temperature DECIMAL(4,2) CHECK (temperature > 30 AND temperature <= 50),
    respiratory_rate INTEGER CHECK (respiratory_rate > 0 AND respiratory_rate <= 100),
    oxygen_saturation INTEGER CHECK (oxygen_saturation >= 0 AND oxygen_saturation <= 100),
    on_ventilator BOOLEAN DEFAULT FALSE,
    on_support BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(patient_id, date)
);

-- Create lab_values table
CREATE TABLE public.lab_values (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID REFERENCES public.patients(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    hemoglobin DECIMAL(4,2) CHECK (hemoglobin > 0 AND hemoglobin <= 30),
    white_blood_cell_count INTEGER CHECK (white_blood_cell_count >= 0),
    platelet_count INTEGER CHECK (platelet_count >= 0),
    creatinine DECIMAL(4,2) CHECK (creatinine >= 0 AND creatinine <= 50),
    bun DECIMAL(4,2) CHECK (bun >= 0 AND bun <= 200),
    glucose INTEGER CHECK (glucose >= 0 AND glucose <= 1000),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(patient_id, date)
);

-- Create medications table
CREATE TABLE public.medications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID REFERENCES public.patients(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    medication_name VARCHAR(100) NOT NULL,
    dosage VARCHAR(50),
    frequency VARCHAR(50),
    route VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create doctor_notes table
CREATE TABLE public.doctor_notes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID REFERENCES public.patients(id) ON DELETE CASCADE,
    note_date DATE NOT NULL,
    note_content TEXT NOT NULL,
    doctor_name VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vital_signs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lab_values ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.medications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.doctor_notes ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (allowing all operations for now - can be restricted later with authentication)
CREATE POLICY "Enable all operations for patients" ON public.patients FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Enable all operations for vital_signs" ON public.vital_signs FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Enable all operations for lab_values" ON public.lab_values FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Enable all operations for medications" ON public.medications FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Enable all operations for doctor_notes" ON public.doctor_notes FOR ALL USING (true) WITH CHECK (true);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp updates on patients table
CREATE TRIGGER update_patients_updated_at
    BEFORE UPDATE ON public.patients
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();