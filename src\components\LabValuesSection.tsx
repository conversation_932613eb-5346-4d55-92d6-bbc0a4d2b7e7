import { useState, useEffect } from "react";
import { Plus, Droplets } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";

interface LabValues {
  id: string;
  date: string;
  hemoglobin: number;
  white_blood_cell_count: number;
  platelet_count: number;
  creatinine: number;
  bun: number;
  glucose: number;
}

interface LabValuesSectionProps {
  patientId: string;
}

const LabValuesSection = ({ patientId }: LabValuesSectionProps) => {
  const [labs, setLabs] = useState<LabValues[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    hemoglobin: "",
    white_blood_cell_count: "",
    platelet_count: "",
    creatinine: "",
    bun: "",
    glucose: "",
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchLabs();
  }, [patientId]);

  const fetchLabs = async () => {
    try {
      const { data, error } = await supabase
        .from("lab_values")
        .select("*")
        .eq("patient_id", patientId)
        .order("date", { ascending: false });

      if (error) throw error;
      setLabs(data || []);
    } catch (error) {
      console.error("Error fetching labs:", error);
      toast({
        title: "Error",
        description: "Failed to fetch lab values",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const { error } = await supabase.from("lab_values").upsert(
        {
          patient_id: patientId,
          date: formData.date,
          hemoglobin: parseFloat(formData.hemoglobin) || null,
          white_blood_cell_count: parseInt(formData.white_blood_cell_count) || null,
          platelet_count: parseInt(formData.platelet_count) || null,
          creatinine: parseFloat(formData.creatinine) || null,
          bun: parseFloat(formData.bun) || null,
          glucose: parseInt(formData.glucose) || null,
        },
        { onConflict: "patient_id,date" }
      );

      if (error) throw error;

      toast({
        title: "Success",
        description: "Lab values saved successfully",
      });

      setIsDialogOpen(false);
      fetchLabs();
      resetForm();
    } catch (error: any) {
      console.error("Error saving labs:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to save lab values",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      date: new Date().toISOString().split('T')[0],
      hemoglobin: "",
      white_blood_cell_count: "",
      platelet_count: "",
      creatinine: "",
      bun: "",
      glucose: "",
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Droplets className="h-5 w-5 text-blue-500" />
          Lab Values
        </CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Lab Results
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add Lab Values</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date">Date</Label>
                  <Input
                    id="date"
                    type="date"
                    value={formData.date}
                    onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="hemoglobin">Hemoglobin (g/dL)</Label>
                  <Input
                    id="hemoglobin"
                    type="number"
                    step="0.1"
                    value={formData.hemoglobin}
                    onChange={(e) => setFormData({ ...formData, hemoglobin: e.target.value })}
                    placeholder="12-16"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="wbc">WBC Count (×10³/μL)</Label>
                  <Input
                    id="wbc"
                    type="number"
                    value={formData.white_blood_cell_count}
                    onChange={(e) => setFormData({ ...formData, white_blood_cell_count: e.target.value })}
                    placeholder="4-11"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="platelets">Platelet Count (×10³/μL)</Label>
                  <Input
                    id="platelets"
                    type="number"
                    value={formData.platelet_count}
                    onChange={(e) => setFormData({ ...formData, platelet_count: e.target.value })}
                    placeholder="150-450"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="creatinine">Creatinine (mg/dL)</Label>
                  <Input
                    id="creatinine"
                    type="number"
                    step="0.1"
                    value={formData.creatinine}
                    onChange={(e) => setFormData({ ...formData, creatinine: e.target.value })}
                    placeholder="0.6-1.2"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bun">BUN (mg/dL)</Label>
                  <Input
                    id="bun"
                    type="number"
                    step="0.1"
                    value={formData.bun}
                    onChange={(e) => setFormData({ ...formData, bun: e.target.value })}
                    placeholder="7-20"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="glucose">Glucose (mg/dL)</Label>
                  <Input
                    id="glucose"
                    type="number"
                    value={formData.glucose}
                    onChange={(e) => setFormData({ ...formData, glucose: e.target.value })}
                    placeholder="70-100"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Save Lab Results</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-8">Loading lab values...</div>
        ) : labs.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No lab values recorded yet. Click "Add Lab Results" to get started.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Hgb (g/dL)</TableHead>
                  <TableHead>WBC (×10³/μL)</TableHead>
                  <TableHead>Platelets (×10³/μL)</TableHead>
                  <TableHead>Creatinine (mg/dL)</TableHead>
                  <TableHead>BUN (mg/dL)</TableHead>
                  <TableHead>Glucose (mg/dL)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {labs.map((lab) => (
                  <TableRow key={lab.id}>
                    <TableCell>{formatDate(lab.date)}</TableCell>
                    <TableCell>{lab.hemoglobin || "-"}</TableCell>
                    <TableCell>{lab.white_blood_cell_count || "-"}</TableCell>
                    <TableCell>{lab.platelet_count || "-"}</TableCell>
                    <TableCell>{lab.creatinine || "-"}</TableCell>
                    <TableCell>{lab.bun || "-"}</TableCell>
                    <TableCell>{lab.glucose || "-"}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default LabValuesSection;